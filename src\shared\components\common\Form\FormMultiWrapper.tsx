import React, { ReactNode } from 'react';
import Typography from '../Typography';

export interface FormMultiWrapperProps {
  /**
   * Tiêu đề hiển thị ở đầu form
   */
  title?: ReactNode;

  /**
   * Nội dung của form (các FormItem, Button, etc.)
   */
  children: ReactNode;

  /**
   * Class bổ sung cho container div
   */
  className?: string;

  /**
   * Class bổ sung cho tiêu đề
   */
  titleClassName?: string;

  /**
   * Class bổ sung cho content wrapper
   */
  contentClassName?: string;

  /**
   * Variant của Typography cho tiêu đề
   * @default 'h3'
   */
  titleVariant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'subtitle1' | 'subtitle2';

  /**
   * M<PERSON>u sắc của tiêu đề
   * @default 'default'
   */
  titleColor?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'muted';

  /**
   * <PERSON><PERSON> đậm của tiêu đề
   * @default 'semibold'
   */
  titleWeight?: 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';

  /**
   * Căn chỉnh tiêu đề
   * @default 'left'
   */
  titleAlign?: 'left' | 'center' | 'right';

  /**
   * Khoảng cách giữa tiêu đề và nội dung
   * @default 'md'
   */
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Hiển thị border dưới tiêu đề
   * @default false
   */
  titleBorder?: boolean;

  /**
   * Padding cho container
   * @default 'md'
   */
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Background cho container
   * @default 'transparent'
   */
  background?: 'transparent' | 'card' | 'muted';

  /**
   * Border radius cho container
   * @default 'md'
   */
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Shadow cho container
   * @default 'none'
   */
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl';

  /**
   * ID cho container
   */
  id?: string;
}

const FormMultiWrapper: React.FC<FormMultiWrapperProps> = ({
  title,
  children,
  className = '',
  titleClassName = '',
  contentClassName = '',
  titleVariant = 'h3',
  titleColor = 'default',
  titleWeight = 'semibold',
  titleAlign = 'left',
  spacing = 'md',
  titleBorder = false,
  padding = 'md',
  background = 'transparent',
  radius = 'md',
  shadow = 'none',
  id,
}) => {
  // Map spacing to Tailwind classes
  const spacingMap = {
    none: 'space-y-0',
    xs: 'space-y-1',
    sm: 'space-y-2',
    md: 'space-y-4',
    lg: 'space-y-6',
    xl: 'space-y-8',
  };

  // Map padding to Tailwind classes
  const paddingMap = {
    none: 'p-0',
    xs: 'p-1',
    sm: 'p-2',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  };

  // Map background to Tailwind classes
  const backgroundMap = {
    transparent: 'bg-transparent',
    card: 'bg-card',
    muted: 'bg-muted',
  };

  // Map radius to Tailwind classes
  const radiusMap = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
  };

  // Map shadow to Tailwind classes
  const shadowMap = {
    none: 'shadow-none',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
  };

  // Container classes
  const containerClasses = [
    'w-full',
    paddingMap[padding],
    backgroundMap[background],
    radiusMap[radius],
    shadowMap[shadow],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Content wrapper classes
  const contentWrapperClasses = [
    spacingMap[spacing],
    contentClassName,
  ]
    .filter(Boolean)
    .join(' ');

  // Title classes
  const titleClasses = [
    titleBorder ? 'border-b border-border pb-2' : '',
    titleClassName,
  ]
    .filter(Boolean)
    .join(' ');

  return (
    <div id={id} className={containerClasses}>
      <div className={contentWrapperClasses}>
        {title && (
          <Typography
            variant={titleVariant}
            color={titleColor}
            weight={titleWeight}
            align={titleAlign}
            className={titleClasses}
          >
            {title}
          </Typography>
        )}
        
        <div className="form-content">
          {children}
        </div>
      </div>
    </div>
  );
};

export default FormMultiWrapper;
